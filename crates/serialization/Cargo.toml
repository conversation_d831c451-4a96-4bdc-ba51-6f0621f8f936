[package]
name = "nautilus-serialization"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_serialization"
crate-type = ["rlib", "staticlib", "cdylib"]

[features]
default = []
extension-module = [
  "pyo3/extension-module",
  "nautilus-core/extension-module",
  "nautilus-model/extension-module",
]
python = ["pyo3", "nautilus-core/python", "nautilus-model/python"]
high-precision = ["nautilus-model/high-precision"]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-core = { workspace = true }
nautilus-model = { workspace = true, features = ["stubs"] }

anyhow = { workspace = true }
arrow = { workspace = true }
parquet = { workspace = true }
pyo3 = { workspace = true, optional = true }
thiserror = { workspace = true }
serde = { workspace = true }
strum = { workspace = true }
object_store = { version = "0.12.0", features = ["aws"] }
tokio = { version = "1.0", features = ["rt", "rt-multi-thread"] }
futures = "0.3"

[dev-dependencies]
nautilus-testkit = { workspace = true }
criterion = { workspace = true }
pretty_assertions = { workspace = true }
rstest = { workspace = true }
